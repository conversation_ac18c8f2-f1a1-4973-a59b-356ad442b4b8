import { MetricUseCase } from '@business-base/application/use-cases/metric.use-case';
import { AccountRoles } from '@common/auth/decorators/account-role.decorator';
import { UserRolesInAccount } from '@common/auth/decorators/user-role-in-account.decorator';
import { AccountRole, GroupByDate, PortfolioItemStatus, UserRoleInAccount } from '@common/enums';
import { Controller, Get, Query, Req, Version } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';

@UserRolesInAccount(UserRoleInAccount.ADMIN)
@AccountRoles(AccountRole.BASIC)
@Controller('business-base/metrics')
export class MetricsController {
  constructor(
    private readonly metricUseCase: MetricUseCase,
    private readonly portfolioUseCase: PortfolioUseCase,
  ) { }

  @Get('portfolio/created')
  @Version('1')
  async getPortfolioCreatedMetrics(
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
    @Req() request: Request,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.metricUseCase.getPortfolioCreatedMetrics(
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: metrics,
    };
  }

  @Get('portfolio/items/created')
  @Version('1')
  async getPortfolioItemCreatedMetrics(
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
    @Req() request: Request,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.metricUseCase.getPortfolioItemCreatedMetrics(
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: metrics,
    };
  }

  @Get('portfolio/items/with-interaction')
  @Version('1')
  async getPortfolioItemsWithInteractionCount(
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
    @Query('groupByDate') groupByDate: GroupByDate,
    @Req() request: Request,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.metricUseCase.getPortfolioItemsWithInteractionCountByDate(
      customerId,
      startDate,
      endDate,
      groupByDate,
    );

    return {
      statusCode: 200,
      data: metrics,
    };
  }

  @ApiOperation({ summary: 'Get portfolio items with only AI interaction count' })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: true,
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: true,
    example: '2024-01-31T23:59:59.999Z',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved AI-only interaction count',
    schema: {
      example: {
        statusCode: 200,
        data: 42,
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Get('portfolio/items/ai-only-interaction')
  @Version('1')
  async getPortfolioItemsWithAiOnlyInteractionCount(
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
    @Req() request: Request,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.metricUseCase.getPortfolioItemsWithOnlyAiInteractionCountByDate(
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: metrics,
    };
  }

  @Get('portfolio/items/grouped-by-date')
  @Version('1')
  async getPortfolioItemsCountByDate(
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
    @Query('groupByDate') groupByDate: GroupByDate,
    @Query('currentStatus') currentStatus: PortfolioItemStatus,
    @Req() request: Request,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.metricUseCase.getPortfolioItemsCountByDateFilteredByStatus(
      customerId,
      startDate,
      endDate,
      groupByDate,
      currentStatus,
    );

    return {
      statusCode: 200,
      data: metrics,
    };
  }

  @ApiOperation({ summary: 'Get total recovered value across all portfolios' })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
    description: 'Start date for recovered value filtering',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
    description: 'End date for recovered value filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Total recovered value across all portfolios',
    schema: {
      example: {
        statusCode: 200,
        data: {
          totalRecoveredValue: 1500000,
          startDate: '2024-01-01T00:00:00.000Z',
          endDate: '2024-12-31T23:59:59.999Z',
        },
      },
    },
  })
  @Get('portfolio/recovered-value')
  @Version('1')
  async getTotalRecoveredValue(
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
    @Req() request: Request,
  ) {
    const { customerId } = request['user'];

    const totalRecoveredValue = await this.portfolioUseCase.getTotalRecoveredValueByCustomer(
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: {
        totalRecoveredValue,
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
      },
    };
  }
}
